import { useEffect, useState } from "react";
import { Bubble, PrimaryButton } from "microapps";
import "./CluesView.scss";

interface Clue {
  id: string;
  text: string;
  sessionId: string;
  timestamp: string;
}

interface CluesData {
  clues: Clue[];
  lastUpdated: string;
  sessionId: string;
}

interface CluesViewProps {
  onBackToMain: () => void;
}

const CluesView: React.FC<CluesViewProps> = ({ onBackToMain }) => {
  const [clues, setClues] = useState<Clue[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadCluesFromLocalStorage();
  }, []);

  const loadCluesFromLocalStorage = () => {
    try {
      const storedData = localStorage.getItem("clues");
      if (storedData) {
        const cluesData: CluesData = JSON.parse(storedData);
        setClues(cluesData.clues || []);
      }
    } catch (error) {
      console.error("Error al cargar las pistas desde localStorage:", error);
      setClues([]);
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      {!loading && (
        <div className="clues-screen">
          <div className="clues-container">
            {clues.length === 0 ? (
              <div className="empty-state">
                <div className="empty-state-frame">
                  <p className="no-clues-message body1">
                    Aquí aparecerán las pistas que vayas descubriendo... Por
                    ahora, los secretos permanecen ocultos en las sombras. Hazme
                    tu primera pregunta y comenzaré a revelarlos.
                  </p>
                </div>
              </div>
            ) : (
              <div className="clues-list">
                {clues.map((clue) => (
                  <div key={clue.id} className="clue-item">
                    <Bubble
                      text={clue.text}
                      backgroundColor="#D6FDF1"
                      textColor="#000"
                      className="clue-tag"
                      id={clue.id}
                    />
                  </div>
                ))}
              </div>
            )}
          </div>
          <div className="clues-screen-footer">
            <PrimaryButton
              onClick={onBackToMain}
              className="go-back-button primary-button"
              text="Volver al juego"
            />
          </div>
        </div>
      )}
    </>
  );
};

export default CluesView;
