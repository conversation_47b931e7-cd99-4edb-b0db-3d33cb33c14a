import { useState, useEffect, useRef } from "react";
import { Image, Mic, Modal } from "microapps";
import { io, Socket } from "socket.io-client";
import { useEnygmaGame } from "../../../contexts/EnygmaGameContext";
import { useQuestionsColor } from "../../../utils/questionsColorSystem";
import "./PlayView.scss";

interface PlayViewProps {
  handleShowClues: () => void;
  handleExistGame: () => void;
  showExitPopup: boolean;
  handleConfirmExit: () => void;
  handleCancelExit: () => void;
}

interface SessionMessage {
  id: string;
  text: string;
  sender: "user" | "ai";
  timestamp: Date;
}

interface ChatMessage {
  id: string;
  text: string;
  sender: "user" | "ai";
  timestamp: Date;
}

const PlayView: React.FC<PlayViewProps> = ({
  handleShowClues,
  handleExistGame,
  showExitPopup,
  handleConfirmExit,
  handleCancelExit,
}) => {
  const { session, askQuestion, askInitialMessage } = useEnygmaGame();
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputText, setInputText] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isLivesPopUpShown, setIsLivesPopUpShown] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Hook para obtener el color basado en preguntas restantes
  const questionsColorData = useQuestionsColor(
    session?.maxQuestions || 20,
    session?.questionCount || 0
  );

  // ========== ESTADOS DE VOZ ==========
  const [micLevel, setMicLevel] = useState(0);
  const [isVoiceActive, setIsVoiceActive] = useState(false);
  const [voiceError, setVoiceError] = useState<string | null>(null);
  const [isSocketConnected, setIsSocketConnected] = useState(false);
  const [isAILoading, setIsAILoading] = useState(false);

  // ========== REFERENCIAS ==========
  const socketRef = useRef<Socket | null>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const playbackSourceRef = useRef<AudioBufferSourceNode | null>(null);
  const mediaStreamRef = useRef<MediaStream | null>(null);

  // ========== FUNCIONES DE AUDIO ==========
  const stopAudio = () => {
    if (playbackSourceRef.current) {
      try {
        playbackSourceRef.current.stop();
        playbackSourceRef.current = null;
      } catch (error) {
        console.error("Error stopping audio:", error);
      }
    }
  };

  const playAudio = async (audioUrl: string) => {
    if (!audioContextRef.current) return;

    try {
      const response = await fetch(audioUrl);
      const arrayBuffer = await response.arrayBuffer();
      const audioBuffer = await audioContextRef.current.decodeAudioData(arrayBuffer);

      if (playbackSourceRef.current) {
        stopAudio();
      }

      const source = audioContextRef.current.createBufferSource();
      source.buffer = audioBuffer;
      source.connect(audioContextRef.current.destination);
      playbackSourceRef.current = source;
      source.start();

      source.onended = () => {
        playbackSourceRef.current = null;
      };
    } catch (error) {
      console.error("Error playing audio:", error);
    }
  };

  // ========== CONFIGURACIÓN DE TRANSCRIPCIÓN (IGUAL QUE APP.JSX) ==========
  const setupTranscription = async () => {
    try {
      console.log("🎤 Iniciando configuración de transcripción...");

      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          channelCount: 1,
          sampleRate: 16000,
          sampleSize: 16,
        },
      });

      console.log("✅ Stream de audio obtenido");

      audioContextRef.current = new AudioContext();
      mediaStreamRef.current = stream;

      console.log("🔧 Sample Rate del AudioContext:", audioContextRef.current.sampleRate);

      // ✅ USAR ARCHIVO EXTERNO (IGUAL QUE APP.JSX)
      await audioContextRef.current.audioWorklet.addModule('/audio-processor.js');
      console.log("✅ AudioWorklet module cargado");

      const source = audioContextRef.current.createMediaStreamSource(stream);
      const processor = new AudioWorkletNode(audioContextRef.current, 'audio-processor');

      console.log("✅ AudioWorkletNode creado");

      const audioBuffer: number[] = [];

      // ========== PROCESAMIENTO DE AUDIO (SIMPLIFICADO COMO APP.JSX) ==========
      processor.port.onmessage = (event) => {
        const audioSamples = event.data;
        const int16Buffer = new Int16Array(audioSamples.length);

        for (let i = 0; i < audioSamples.length; i++) {
          int16Buffer[i] = Math.max(-32768, Math.min(32767, audioSamples[i] * 32768));
        }

        audioBuffer.push(...int16Buffer);

        if (audioBuffer.length >= 1600) { // 100ms chunks
          const chunkToSend = new Int16Array(audioBuffer.splice(0, 1600));
          if (socketRef.current?.connected) {
            console.log("📤 Enviando chunk de audio:", chunkToSend.length, "samples");
            socketRef.current.emit('audio', chunkToSend.buffer);
          } else {
            console.warn("⚠️ Socket no conectado, no se puede enviar audio");
          }
        }
      };

      source.connect(processor);

      // ========== MONITOREO DE NIVEL (SIMPLIFICADO) ==========
      const analyser = audioContextRef.current.createAnalyser();
      analyser.fftSize = 2048;
      source.connect(analyser);

      const dataArray = new Uint8Array(analyser.frequencyBinCount);
      const threshold = 0.1;

      const updateMicLevel = () => {
        if (!audioContextRef.current) return;

        analyser.getByteTimeDomainData(dataArray);
        let sum = 0;
        for (let i = 0; i < dataArray.length; i++) {
          const v = (dataArray[i] - 128) / 128;
          sum += v * v;
        }
        const rms = Math.sqrt(sum / dataArray.length);
        setMicLevel(rms);

        // Detectar voz y detener audio si es necesario
        if (rms > threshold && playbackSourceRef.current) {
          stopAudio();
        }

        requestAnimationFrame(updateMicLevel);
      };

      updateMicLevel();

      setIsVoiceActive(true);
      setVoiceError(null);
      console.log("✅ Transcripción configurada correctamente");

    } catch (error) {
      console.error("❌ Error setting up transcription:", error);
      setVoiceError(error instanceof Error ? error.message : "Error setting up audio");
      setIsVoiceActive(false);
    }
  };

  const stopTranscription = async () => {
    try {
      console.log("🔇 Deteniendo transcripción...");
      setIsVoiceActive(false);

      if (playbackSourceRef.current) {
        stopAudio();
      }

      if (mediaStreamRef.current) {
        const tracks = mediaStreamRef.current.getTracks();
        for (const track of tracks) {
          track.enabled = false;
          setTimeout(() => {
            track.stop();
          }, 50);
        }
        mediaStreamRef.current = null;
      }

      if (audioContextRef.current) {
        if (audioContextRef.current.state !== 'closed' && audioContextRef.current.state !== 'suspended') {
          await audioContextRef.current.suspend();
        }

        await new Promise((resolve) => setTimeout(resolve, 100));

        if (audioContextRef.current && audioContextRef.current.state !== 'closed') {
          await audioContextRef.current.close();
        }

        audioContextRef.current = null;
      }

      console.log("✅ Transcripción detenida");
    } catch (error) {
      console.error("❌ Error stopping transcription:", error);
    }
  };

  // ========== CONEXIÓN SOCKET (IGUAL QUE APP.JSX) ==========
  const connectSocket = async (): Promise<void> => {
    return new Promise((resolve, reject) => {
      console.log("🔌 Iniciando conexión socket...");

      const apiUrl = import.meta.env.VITE_FLUID_VOICE_API_URL;
      const apiKey = import.meta.env.VITE_FLUID_VOICE_API_KEY;

      console.log("🔧 Configuración API:", {
        url: apiUrl,
        hasKey: !!apiKey,
        keyLength: apiKey?.length || 0
      });

      if (!apiUrl || !apiKey) {
        const error = new Error("Variables de entorno no configuradas");
        console.error("❌ Faltan variables:", { apiUrl, hasKey: !!apiKey });
        reject(error);
        return;
      }

      try {
        // ✅ CONFIGURACIÓN EXACTA DE APP.JSX
        socketRef.current = io(apiUrl, {
          auth: {
            apiKey: apiKey,
          },
          transports: ['websocket', 'polling'],
        });

        console.log("✅ Socket.IO inicializado");

        if (socketRef.current) {
          setupSocketListeners(socketRef.current);

          socketRef.current.on("connect", () => {
            console.log("✅ Socket conectado exitosamente");
            setIsSocketConnected(true);
            resolve();
          });

          socketRef.current.on("connect_error", (error) => {
            console.error("❌ Error de conexión socket:", error);
            setIsSocketConnected(false);
            reject(error);
          });

          socketRef.current.on("disconnect", (reason) => {
            console.log("🔌 Socket desconectado:", reason);
            setIsSocketConnected(false);
          });
        }
      } catch (error) {
        console.error("❌ Error inicializando socket:", error);
        reject(error);
      }
    });
  };

  // ========== LISTENERS DE SOCKET (EXACTOS DE APP.JSX) ==========
  const setupSocketListeners = (socket: Socket) => {
    console.log("⚙️ Configurando listeners del socket...");

    socket.on('transcription', (newTranscription: string) => {
      console.log("📝 Transcripción recibida:", newTranscription);

      if (playbackSourceRef.current) {
        stopAudio();
      }

      const userMessage: ChatMessage = {
        id: `user-${Date.now()}`,
        text: newTranscription,
        sender: "user",
        timestamp: new Date(),
      };
      setMessages((prev) => [...prev, userMessage]);
    });

    socket.on('loading', () => {
      console.log("⏳ IA procesando...");
      setIsAILoading(true);
      // Detener transcripción mientras la IA procesa (como en App.jsx)
      stopTranscription();
    });

    socket.on('reply', async ({ text, audioUrl }: { text: string; audioUrl?: string }) => {
      console.log("💬 Respuesta recibida:", { text, hasAudio: !!audioUrl });
      setIsAILoading(false);

      // ========== PROCESAMIENTO DE RESPUESTAS (IGUAL QUE APP.JSX) ==========
      if (text.startsWith('[EXIT]')) {
        console.log("🚪 Comando de salida");
      } else if (text.startsWith('[PAUSE]')) {
        console.log("⏸️ Comando de pausa");
        setIsVoiceActive(false);
        const pauseMessage: ChatMessage = {
          id: `pause-${Date.now()}`,
          text: 'Estoy esperando, indica "Ok Aura, continuar" para seguir con la conversación',
          sender: "ai",
          timestamp: new Date(),
        };
        setMessages((prev) => [...prev, pauseMessage]);
      } else if (text.startsWith('[COMMAND]')) {
        console.log("🎮 Comando:", text);
      } else if (text.startsWith('[NONE]')) {
        console.log("❌ Respuesta NONE");
        const noneMessage: ChatMessage = {
          id: `none-${Date.now()}`,
          text: 'No puedo ayudarte con eso. Te puedo ayudar a darte una recomendación personalizada, ¿Qué te apetece ver hoy?',
          sender: "ai",
          timestamp: new Date(),
        };
        setMessages((prev) => [...prev, noneMessage]);
        // Reanudar transcripción
        await new Promise((resolve) => setTimeout(resolve, 1000));
        await setupTranscription();
      } else if (text === '[OK AURA]') {
        console.log("🤖 Respuesta OK AURA");
        const auraMessage: ChatMessage = {
          id: `aura-${Date.now()}`,
          text: 'Recuerda que en esta experiencia de voz no necesitas volver a indicar "Ok Aura" mientras el micrófono esté activo',
          sender: "ai",
          timestamp: new Date(),
        };
        setMessages((prev) => [...prev, auraMessage]);
        // Reanudar transcripción
        await new Promise((resolve) => setTimeout(resolve, 1000));
        await setupTranscription();
      } else if (text.startsWith('[JSON]')) {
        console.log("📋 Respuesta JSON");
      } else {
        // Respuesta normal
        console.log("💬 Respuesta normal de IA");
        const aiMessage: ChatMessage = {
          id: `ai-${Date.now()}`,
          text: text,
          sender: "ai",
          timestamp: new Date(),
        };
        setMessages((prev) => [...prev, aiMessage]);

        // Reproducir audio si está disponible
        if (audioUrl) {
          console.log("🔊 Reproduciendo audio...");
          await playAudio(audioUrl);
        }

        // Reanudar transcripción después de un breve delay
        await new Promise((resolve) => setTimeout(resolve, 1000));
        await setupTranscription();
      }
    });

    // Eventos de error y conexión
    socket.on('error', (error) => {
      console.error("❌ Error de socket:", error);
    });

    socket.on('connect_error', (error) => {
      console.error("❌ Error de conexión:", error);
    });

    socket.on('disconnect', (reason) => {
      console.log("🔌 Socket desconectado:", reason);
      setIsSocketConnected(false);
    });

    socket.on('reconnect', () => {
      console.log("🔄 Socket reconectado");
      setIsSocketConnected(true);
      setVoiceError(null);
    });

    console.log("✅ Listeners configurados");
  };

  // ========== FUNCIONES DE PRUEBA ==========
  const testConversation = async () => {
    console.log("🧪 === INICIANDO TEST DE CONVERSACIÓN ===");

    try {
      // 1. Conectar socket
      if (!isSocketConnected) {
        console.log("🔌 Conectando socket...");
        await connectSocket();
        console.log("✅ Socket conectado");
      }

      // 2. Configurar transcripción
      if (!isVoiceActive) {
        console.log("🎤 Configurando transcripción...");
        await setupTranscription();
        console.log("✅ Transcripción configurada");
      }

      console.log("✅ === CONVERSACIÓN LISTA ===");

      const readyMessage: ChatMessage = {
        id: `ready-${Date.now()}`,
        text: "🎤 Sistema de voz activado. Puedes hablar ahora...",
        sender: "ai",
        timestamp: new Date(),
      };
      setMessages((prev) => [...prev, readyMessage]);

    } catch (error) {
      console.error("❌ Error en test de conversación:", error);
      const errorMessage: ChatMessage = {
        id: `error-${Date.now()}`,
        text: `Error: ${error instanceof Error ? error.message : 'Error desconocido'}`,
        sender: "ai",
        timestamp: new Date(),
      };
      setMessages((prev) => [...prev, errorMessage]);
    }
  };

  const testServerEvents = () => {
    console.log("🧪 === PROBANDO EVENTOS DEL SERVIDOR ===");

    if (!socketRef.current?.connected) {
      console.error("❌ Socket no conectado");
      return;
    }

    // Probar eventos que SÍ funcionan en App.jsx
    console.log("📤 Enviando 'mute-sound'");
    socketRef.current.emit('mute-sound');

    setTimeout(() => {
      console.log("📤 Enviando 'unmute-sound'");
      socketRef.current?.emit('unmute-sound');
    }, 2000);

    console.log("✅ Eventos enviados. Verifica la respuesta del servidor.");
  };

  // ========== INICIALIZACIÓN ==========
  useEffect(() => {
    const initialize = async () => {
      console.log("🚀 === INICIALIZANDO PLAYVIEW ===");

      try {
        console.log("🔌 Conectando socket...");
        await connectSocket();

        console.log("⏰ Esperando 2 segundos antes de configurar audio...");
        setTimeout(async () => {
          try {
            await setupTranscription();
            console.log("✅ === INICIALIZACIÓN COMPLETA ===");
          } catch (audioError) {
            console.error("❌ Error configurando audio:", audioError);
          }
        }, 2000);

      } catch (error) {
        console.error("❌ Error en inicialización:", error);
        setVoiceError(error instanceof Error ? error.message : "Error de inicialización");
      }
    };

    initialize();

    // Cleanup
    return () => {
      console.log("🧹 Limpiando recursos...");
      if (socketRef.current) {
        socketRef.current.disconnect();
        socketRef.current = null;
      }
      stopTranscription();
    };
  }, []);

  const toggleVoice = async () => {
    if (isVoiceActive) {
      console.log("🔇 Apagando voz...");
      await stopTranscription();
    } else {
      console.log("🎤 Encendiendo voz...");
      await setupTranscription();
    }
  };

  // Auto-scroll
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Sync messages from game session
  useEffect(() => {
    if (session?.messages) {
      const chatMessages: ChatMessage[] = session.messages.map(
        (msg: SessionMessage): ChatMessage => ({
          id: msg.id,
          text: msg.text,
          sender: msg.sender,
          timestamp: msg.timestamp,
        })
      );
      setMessages(chatMessages);
    }
  }, [session?.messages]);

  useEffect(() => {
    if (
      session &&
      (!session.messages || session.messages.length === 0) &&
      messages.length === 0
    ) {
      const sendInitialHola = async () => {
        try {
          setIsLoading(true);
          await askInitialMessage("Hola");
        } catch (error) {
          console.error("Error sending initial Hola:", error);
        } finally {
          setIsLoading(false);
        }
      };

      sendInitialHola();
    }
  }, [session, messages.length, askInitialMessage]);

  const handleSendMessage = async () => {
    if (!inputText.trim() || isLoading || !session) return;

    const messageText = inputText.trim();
    setInputText("");
    setIsLoading(true);

    try {
      await askQuestion(messageText);
    } catch (error) {
      const errorMessage: ChatMessage = {
        id: `error-${Date.now()}`,
        text: "Lo siento, hubo un error al procesar tu mensaje. Inténtalo de nuevo.",
        sender: "ai",
        timestamp: new Date(),
      };
      setMessages((prev) => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <>
      <div className="chat-view">
        <div className="menu-left">
          <div className="enygma-logo">
            {/* ========== BOTONES DE DEBUG ========== */}
            <div style={{
              position: "relative",
              top: "-120px",
              display: "flex",
              flexDirection: "column",
              gap: "5px",
              marginBottom: "10px"
            }}>
              <button
                onClick={testConversation}
                style={{
                  background: isVoiceActive ? "green" : "blue",
                  color: "white",
                  padding: "8px 12px",
                  border: "none",
                  borderRadius: "5px",
                  cursor: "pointer",
                  fontSize: "12px",
                }}
              >
                {isVoiceActive ? "🎤 Voz ON" : "🎤 Activar Voz"}
              </button>

              <button
                onClick={testServerEvents}
                style={{
                  background: isSocketConnected ? "orange" : "gray",
                  color: "white",
                  padding: "8px 12px",
                  border: "none",
                  borderRadius: "5px",
                  cursor: "pointer",
                  fontSize: "12px",
                }}
              >
                {isSocketConnected ? "🧪 Test Server" : "❌ Sin Socket"}
              </button>

              {/* Estado detallado */}
              <div style={{
                fontSize: "10px",
                color: "white",
                textAlign: "center"
              }}>
                Socket: {isSocketConnected ? "🟢" : "🔴"}<br/>
                Mic: {isVoiceActive ? "🟢" : "🔴"}<br/>
                Level: {Math.round(micLevel * 100)}%<br/>
                AI: {isAILoading ? "🤔" : "💤"}
              </div>
            </div>

            <Image
              src="assets/game/enygma.png"
              alt="Enygma"
              className="enygma-image"
              width="180px"
              aspectRatio="1:1"
            />

            <div className="speaking">
              <Mic
                level={Math.round(micLevel * 100)}
                onClick={toggleVoice}
                state={
                  isVoiceActive
                    ? "recording"
                    : voiceError
                      ? "disabled"
                      : "default"
                }
              />
              {voiceError && (
                <div
                  className="voice-error"
                  style={{ color: "red", fontSize: "12px", marginTop: "4px" }}
                >
                  {voiceError}
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="chat-view-wrapper">
          <div className="chat-container">
            <div
              className={`chat-content ${
                messages.length > 0 &&
                messages[messages.length - 1].sender === "user"
                  ? "align-right"
                  : "align-left"
              }`}
            >
              <div className="chat-text body1">
                {isAILoading
                  ? "🤔 IA procesando..."
                  : messages.length > 0
                    ? messages[messages.length - 1].text
                    : isSocketConnected
                      ? isVoiceActive
                        ? "🎤 Escuchando... Habla ahora"
                        : "🎤 Activa el micrófono para comenzar"
                      : "⏳ Conectando al servidor..."}
              </div>
            </div>
          </div>

          <div className="chat-input-container">
            <div className="input-wrapper">
              <input
                type="text"
                value={inputText}
                onChange={(e) => setInputText(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder="Haz una pregunta sobre el personaje..."
                disabled={isLoading || !session}
                className="chat-input"
              />
              <button
                onClick={handleSendMessage}
                disabled={!inputText.trim() || isLoading || !session}
                className="send-button"
              >
                {isLoading ? "Enviando..." : "Enviar"}
              </button>
            </div>
          </div>
        </div>

        <div className="menu-right">
          <div
            onClick={() => setIsLivesPopUpShown((prev) => !prev)}
            className="image-button"
          >
            <Image
              width="100%"
              aspectRatio="1:1"
              src="assets/game/lives.png"
              alt="Vidas"
              className="book-image"
            />

            {session && (
              <p
                className={`body2 bold questions-color-text ${questionsColorData.colorClass}`}
                style={{
                  color: questionsColorData.hexColor,
                }}
              >
                {session?.maxQuestions! - session?.questionCount!}
              </p>
            )}
          </div>

          <div onClick={handleShowClues} className="image-button">
            <Image
              width="100%"
              aspectRatio="1:1"
              src="assets/game/clues.png"
              alt="Pistas"
              className="clues-image"
            />
            <p className="body2 bold">Pistas</p>
          </div>

          <div onClick={handleExistGame} className="image-button">
            <Image
              width="100%"
              aspectRatio="1:1"
              src="assets/game/exit.png"
              alt="Salir"
              className="exit-image"
            />
            <p className="body2 bold">Salir</p>
          </div>
        </div>
      </div>

      {showExitPopup && (
        <Modal
          title="¿Seguro que quieres salir del juego?"
          onClose={handleCancelExit}
          onCancel={handleConfirmExit}
          onConfirm={handleCancelExit}
          cancelText="Salir de todos modos"
          confirmText="Seguir jugando"
          body="Si sales ahora, vas a perder tu progreso actual. Puedes seguir jugando o salir cuando quieras."
        />
      )}

      {isLivesPopUpShown && (
        <Modal
          title="Tus preguntas restantes"
          onClose={() => setIsLivesPopUpShown((prev) => !prev)}
          onConfirm={() => setIsLivesPopUpShown((prev) => !prev)}
          confirmText="Entendido"
          body=" Tienes un máximo de 20 preguntas para adivinar la respuesta. Cada
          vez que haces una, se descuenta del contador. Piensa bien cada
          pregunta: ¡cada una cuenta!"
        />
      )}
    </>
  );
};

export default PlayView;
